<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.service.ImageReferenceServiceTest" time="0.531" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\sdplc\target\test-classes;D:\sdplc\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-thymeleaf\2.3.12.RELEASE\spring-boot-starter-thymeleaf-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf-spring5\3.0.12.RELEASE\thymeleaf-spring5-3.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf\3.0.12.RELEASE\thymeleaf-3.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\attoparser\attoparser\2.0.5.RELEASE\attoparser-2.0.5.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\extras\thymeleaf-extras-java8time\3.0.4.RELEASE\thymeleaf-extras-java8time-3.0.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.3.12.RELEASE\spring-boot-starter-websocket-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.2.15.RELEASE\spring-messaging-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.2.15.RELEASE\spring-websocket-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\eclipse\paho\org.eclipse.paho.client.mqttv3\1.2.5\org.eclipse.paho.client.mqttv3-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\ghgande\j2mod\3.1.1\j2mod-3.1.1.jar;C:\Users\<USER>\.m2\repository\com\fazecast\jSerialComm\2.8.5\jSerialComm-2.8.5.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-core\3.9.15\vertx-core-3.9.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.65.Final\netty-common-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.65.Final\netty-buffer-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.65.Final\netty-transport-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.65.Final\netty-handler-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.65.Final\netty-codec-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.65.Final\netty-handler-proxy-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.65.Final\netty-codec-socks-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.65.Final\netty-codec-http-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.65.Final\netty-codec-http2-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.65.Final\netty-resolver-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.65.Final\netty-resolver-dns-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.65.Final\netty-codec-dns-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-mqtt\3.9.15\vertx-mqtt-3.9.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.65.Final\netty-codec-mqtt-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\testcontainers\1.15.1\testcontainers-1.15.1.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.20\commons-compress-1.20.jar;C:\Users\<USER>\.m2\repository\org\rnorth\duct-tape\duct-tape\1.0.8\duct-tape-1.0.8.jar;C:\Users\<USER>\.m2\repository\org\rnorth\visible-assertions\visible-assertions\2.1.2\visible-assertions-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.2.0\jna-5.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-api\3.2.7\docker-java-api-3.2.7.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport-zerodep\3.2.7\docker-java-transport-zerodep-3.2.7.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport\3.2.7\docker-java-transport-3.2.7.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\infiniteautomation\modbus4j\3.0.3\modbus4j-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.3.9.RELEASE\spring-security-crypto-5.3.9.RELEASE.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Java\jdk-1.8\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2281105416575476514\surefirebooter5808451504607738683.jar C:\Users\<USER>\AppData\Local\Temp\surefire2281105416575476514 2025-08-01T10-11-35_236-jvmRun1 surefire8682268658751734465tmp surefire_0247097891476277074tmp"/>
    <property name="surefire.test.class.path" value="D:\sdplc\target\test-classes;D:\sdplc\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-thymeleaf\2.3.12.RELEASE\spring-boot-starter-thymeleaf-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf-spring5\3.0.12.RELEASE\thymeleaf-spring5-3.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf\3.0.12.RELEASE\thymeleaf-3.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\attoparser\attoparser\2.0.5.RELEASE\attoparser-2.0.5.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\extras\thymeleaf-extras-java8time\3.0.4.RELEASE\thymeleaf-extras-java8time-3.0.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.3.12.RELEASE\spring-boot-starter-websocket-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.2.15.RELEASE\spring-messaging-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.2.15.RELEASE\spring-websocket-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\eclipse\paho\org.eclipse.paho.client.mqttv3\1.2.5\org.eclipse.paho.client.mqttv3-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\ghgande\j2mod\3.1.1\j2mod-3.1.1.jar;C:\Users\<USER>\.m2\repository\com\fazecast\jSerialComm\2.8.5\jSerialComm-2.8.5.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-core\3.9.15\vertx-core-3.9.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.65.Final\netty-common-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.65.Final\netty-buffer-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.65.Final\netty-transport-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.65.Final\netty-handler-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.65.Final\netty-codec-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.65.Final\netty-handler-proxy-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.65.Final\netty-codec-socks-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.65.Final\netty-codec-http-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.65.Final\netty-codec-http2-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.65.Final\netty-resolver-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.65.Final\netty-resolver-dns-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.65.Final\netty-codec-dns-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-mqtt\3.9.15\vertx-mqtt-3.9.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.65.Final\netty-codec-mqtt-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\testcontainers\1.15.1\testcontainers-1.15.1.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.20\commons-compress-1.20.jar;C:\Users\<USER>\.m2\repository\org\rnorth\duct-tape\duct-tape\1.0.8\duct-tape-1.0.8.jar;C:\Users\<USER>\.m2\repository\org\rnorth\visible-assertions\visible-assertions\2.1.2\visible-assertions-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.2.0\jna-5.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-api\3.2.7\docker-java-api-3.2.7.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport-zerodep\3.2.7\docker-java-transport-zerodep-3.2.7.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport\3.2.7\docker-java-transport-3.2.7.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\infiniteautomation\modbus4j\3.0.3\modbus4j-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.3.9.RELEASE\spring-security-crypto-5.3.9.RELEASE.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\jdk-1.8\jre"/>
    <property name="basedir" value="D:\sdplc"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2281105416575476514\surefirebooter5808451504607738683.jar"/>
    <property name="sun.boot.class.path" value="C:\Java\jdk-1.8\jre\lib\resources.jar;C:\Java\jdk-1.8\jre\lib\rt.jar;C:\Java\jdk-1.8\jre\lib\jsse.jar;C:\Java\jdk-1.8\jre\lib\jce.jar;C:\Java\jdk-1.8\jre\lib\charsets.jar;C:\Java\jdk-1.8\jre\lib\jfr.jar;C:\Java\jdk-1.8\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_431-b10"/>
    <property name="user.name" value="Administrator"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Java\jdk-1.8\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2024.3"/>
    <property name="java.version" value="1.8.0_431"/>
    <property name="user.dir" value="D:\sdplc"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Java\jdk-1.8\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Java\jdk-1.8\bin;C:\Java\jdk-1.8\jre\bin;C:\apache-maven-3.5.4\bin;C:\Program Files\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Tencent\QQGameTempest\Hall.58195\;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.431-b10"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="C:\Java\jdk-1.8\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testCheckImageUsage_WithSystemImage" classname="com.example.service.ImageReferenceServiceTest" time="0.509"/>
  <testcase name="testCheckImageUsage_WithMultipleReferences" classname="com.example.service.ImageReferenceServiceTest" time="0.01"/>
  <testcase name="testCheckImageUsage_WithDeviceReference" classname="com.example.service.ImageReferenceServiceTest" time="0.001"/>
  <testcase name="testCheckImageUsage_WithTopologyReference" classname="com.example.service.ImageReferenceServiceTest" time="0.002"/>
  <testcase name="testCheckImageUsage_WithTopologyImageNodeReference" classname="com.example.service.ImageReferenceServiceTest" time="0.001"/>
  <testcase name="testCheckImageUsage_NoReferences" classname="com.example.service.ImageReferenceServiceTest" time="0.002"/>
</testsuite>