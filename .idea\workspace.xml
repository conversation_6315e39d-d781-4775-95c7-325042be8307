<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0b9a55f5-3a97-4f22-a10f-81ab2b557ce1" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\apache-maven-3.5.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2rPkDQNp80UTXzLmGDDmc4ofP3P" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.SHENGDA-PLC [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.SHENGDA-PLC [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.SHENGDA-PLC [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.modbus-mqtt-web [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.modbus-mqtt-web [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ModbusMqttWebApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;&lt;未知&gt;&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/sdplc&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;全局库&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.language&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="sdplc" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="sdplc" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="ModbusMqttWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="modbus-mqtt-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.ModbusMqttWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0b9a55f5-3a97-4f22-a10f-81ab2b557ce1" name="更改" comment="" />
      <created>1736467978080</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1736467978080</updated>
      <workItem from="1736467979342" duration="17988000" />
      <workItem from="1736564296907" duration="2014000" />
      <workItem from="1736567074626" duration="1110000" />
      <workItem from="1736568464967" duration="659000" />
      <workItem from="1736575002376" duration="314000" />
      <workItem from="1736736890917" duration="441000" />
      <workItem from="1736739103757" duration="5994000" />
      <workItem from="1736760283645" duration="5131000" />
      <workItem from="1739868892771" duration="8251000" />
      <workItem from="1740447780577" duration="2082000" />
      <workItem from="1740452209495" duration="24940000" />
      <workItem from="1740728838793" duration="4750000" />
      <workItem from="1740980706950" duration="59045000" />
      <workItem from="1741685401057" duration="20280000" />
      <workItem from="1742194636611" duration="4916000" />
      <workItem from="1742281724498" duration="6865000" />
      <workItem from="1745905707260" duration="13703000" />
      <workItem from="1746433997414" duration="2000" />
      <workItem from="1746434139953" duration="3608000" />
      <workItem from="1746497370581" duration="6999000" />
      <workItem from="1746860624325" duration="2451000" />
      <workItem from="1747010442630" duration="7145000" />
      <workItem from="1747991856679" duration="10352000" />
      <workItem from="1748326580959" duration="4952000" />
      <workItem from="1749009131516" duration="8151000" />
      <workItem from="1749189393103" duration="3589000" />
      <workItem from="1749459371814" duration="1406000" />
      <workItem from="1749515563826" duration="30333000" />
      <workItem from="1749600064567" duration="1637000" />
      <workItem from="1749633475125" duration="34960000" />
      <workItem from="1749773085093" duration="54578000" />
      <workItem from="1749959288195" duration="5631000" />
      <workItem from="1749971046923" duration="4497000" />
      <workItem from="1750032286230" duration="1179000" />
      <workItem from="1750034686960" duration="42593000" />
      <workItem from="1750295342628" duration="43895000" />
      <workItem from="1750487028593" duration="1633000" />
      <workItem from="1750659250672" duration="1099000" />
      <workItem from="1750739120881" duration="13382000" />
      <workItem from="1750835259577" duration="3419000" />
      <workItem from="1750904243626" duration="8845000" />
      <workItem from="1750920984154" duration="7031000" />
      <workItem from="1751007050537" duration="17231000" />
      <workItem from="1751274179121" duration="1845000" />
      <workItem from="1752280077943" duration="30073000" />
      <workItem from="1752541117790" duration="4986000" />
      <workItem from="1752633359374" duration="844000" />
      <workItem from="1752891211278" duration="38582000" />
      <workItem from="1753153276568" duration="7742000" />
      <workItem from="1753176518322" duration="71823000" />
      <workItem from="1753518337345" duration="1484000" />
      <workItem from="1753520149582" duration="11000" />
      <workItem from="1753520164059" duration="207000" />
      <workItem from="1753521330902" duration="1043000" />
      <workItem from="1753523006641" duration="73000" />
      <workItem from="1753523532877" duration="6131000" />
      <workItem from="1753674254385" duration="445000" />
      <workItem from="1753770832631" duration="24888000" />
      <workItem from="1753954063270" duration="5139000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>